# 省市区乡镇四级选择器组件

## 功能特点

1. **四级联动选择**：支持省、市、区县、乡镇四级地区选择
2. **懒加载**：数据按需加载，提高性能
3. **灵活配置**：可配置选择级别（3级或4级）
4. **编辑模式适配**：支持编辑和显示两种模式
5. **数据格式统一**：返回标准的codes和names数组

## 使用方法

### 基本用法

```vue
<template>
  <view>
    <AreaPicker 
      :value="selectedArea" 
      :isEditing="true"
      :level="4"
      placeholder="请选择地区"
      @change="handleAreaChange"
    />
  </view>
</template>

<script>
import AreaPicker from '@/components/AreaPicker.vue'

export default {
  components: {
    AreaPicker
  },
  data() {
    return {
      selectedArea: {
        codes: ['110000', '110100', '110101', '110101001'],
        names: ['北京市', '北京市', '东城区', '东华门街道']
      }
    }
  },
  methods: {
    handleAreaChange(value) {
      this.selectedArea = value
      console.log('选择的地区:', value)
    }
  }
}
</script>
```

### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | Object | `{ codes: [], names: [] }` | 当前选中的地区数据 |
| placeholder | String | '请选择地区' | 占位符文本 |
| isEditing | Boolean | false | 是否为编辑模式 |
| level | Number | 4 | 选择级别，3=省市区，4=省市区乡镇 |

### 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | value: Object | 地区选择变化时触发 |

### 数据格式

组件接收和返回的数据格式：

```javascript
{
  codes: ['110000', '110100', '110101', '110101001'], // 地区代码数组
  names: ['北京市', '北京市', '东城区', '东华门街道']    // 地区名称数组
}
```

## API接口

组件需要以下API接口支持：

### 1. 获取省份列表
```javascript
// GET /manage/eHealthRecord/getProvinces
// 返回格式：
{
  status: 200,
  data: [
    { label: "北京市", value: "110000" },
    { label: "天津市", value: "120000" }
  ]
}
```

### 2. 获取城市列表
```javascript
// GET /manage/eHealthRecord/getCitiesByProvince?provinceCode=110000
// 返回格式：
{
  status: 200,
  data: [
    { label: "北京市", value: "110100" }
  ]
}
```

### 3. 获取区县列表
```javascript
// GET /manage/eHealthRecord/getDistrictsByCity?cityCode=110100
// 返回格式：
{
  status: 200,
  data: [
    { label: "东城区", value: "110101" },
    { label: "西城区", value: "110102" }
  ]
}
```

### 4. 获取乡镇列表
```javascript
// GET /manage/eHealthRecord/getTownsByDistrict?districtCode=110101
// 返回格式：
{
  status: 200,
  data: [
    { label: "东华门街道", value: "110101001" },
    { label: "景山街道", value: "110101002" }
  ]
}
```

## 注意事项

1. **模拟数据**：当前组件使用模拟数据，实际项目中需要替换为真实的API调用
2. **错误处理**：组件内置了错误处理和加载状态
3. **性能优化**：数据懒加载，只在需要时获取下级数据
4. **兼容性**：同时更新新的地区选择器数据和原有的字符串字段，保持向后兼容

## 集成到现有项目

在 `pages_user/pages/eHealthRecord/basicInfo.vue` 中已经集成了该组件：

1. 替换了户籍所在地和常住所在地的输入框
2. 添加了相应的数据处理方法
3. 保存时同时更新新旧两种数据格式

## 后续优化

1. **缓存机制**：添加地区数据缓存，减少重复请求
2. **搜索功能**：支持地区名称搜索
3. **自定义样式**：支持更多样式自定义选项
4. **国际化**：支持多语言
