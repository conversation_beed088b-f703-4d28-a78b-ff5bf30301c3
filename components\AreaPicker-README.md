# 省市区乡镇四级选择器组件

## 功能特点

1. **四级联动选择**：支持省、市、区县、乡镇四级地区选择
2. **懒加载**：数据按需加载，提高性能
3. **灵活配置**：可配置选择级别（3级或4级）
4. **编辑模式适配**：支持编辑和显示两种模式
5. **数据格式统一**：返回标准的codes和names数组

## 使用方法

### 基本用法

```vue
<template>
  <view>
    <!-- 四级选择器 -->
    <AreaPicker
      :value="selectedArea4"
      :isEditing="true"
      :level="4"
      placeholder="请选择省市区乡镇"
      @change="handleArea4Change"
    />

    <!-- 三级选择器 -->
    <AreaPicker
      :value="selectedArea3"
      :isEditing="true"
      :level="3"
      placeholder="请选择省市区"
      @change="handleArea3Change"
    />

    <!-- 二级选择器 -->
    <AreaPicker
      :value="selectedArea2"
      :isEditing="true"
      :level="2"
      placeholder="请选择省市"
      @change="handleArea2Change"
    />

    <!-- 一级选择器 -->
    <AreaPicker
      :value="selectedArea1"
      :isEditing="true"
      :level="1"
      placeholder="请选择省份"
      @change="handleArea1Change"
    />
  </view>
</template>

<script>
import AreaPicker from '@/components/AreaPicker.vue'

export default {
  components: {
    AreaPicker
  },
  data() {
    return {
      selectedArea1: [], // 一级：['北京市']
      selectedArea2: [], // 二级：['北京市', '北京市']
      selectedArea3: [], // 三级：['北京市', '北京市', '东城区']
      selectedArea4: []  // 四级：['北京市', '北京市', '东城区', '东华门街道']
    }
  },
  methods: {
    handleArea1Change(value) {
      this.selectedArea1 = value
      console.log('选择的省份:', value)
    },
    handleArea2Change(value) {
      this.selectedArea2 = value
      console.log('选择的省市:', value)
    },
    handleArea3Change(value) {
      this.selectedArea3 = value
      console.log('选择的省市区:', value)
    },
    handleArea4Change(value) {
      this.selectedArea4 = value
      console.log('选择的省市区乡镇:', value)
    }
  }
}
</script>
```

### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | Array | `[]` | 当前选中的地区名称数组 |
| placeholder | String | '请选择地区' | 占位符文本 |
| isEditing | Boolean | false | 是否为编辑模式 |
| level | Number | 4 | 选择级别，1=省，2=省市，3=省市区，4=省市区乡镇 |

### 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | value: Array | 地区选择变化时触发，返回地区名称数组 |

### 数据格式

组件接收和返回的数据格式为地区名称数组：

```javascript
// 一级选择（省）
['北京市']

// 二级选择（省市）
['北京市', '北京市']

// 三级选择（省市区）
['北京市', '北京市', '东城区']

// 四级选择（省市区乡镇）
['北京市', '北京市', '东城区', '东华门街道']
```

### 兼容性

组件同时支持新旧两种数据格式：

```javascript
// 新格式（推荐）
value: ['北京市', '北京市', '东城区', '东华门街道']

// 旧格式（兼容）
value: {
  codes: ['110000', '110100', '110101', '110101001'],
  names: ['北京市', '北京市', '东城区', '东华门街道']
}
```

## API接口

组件使用统一的地区数据接口：

### 统一地区数据接口
```javascript
// GET /app/allUser/findDistricts
// 参数说明：
// - level: 地区级别 (0=省份, 1=城市, 2=区县, 3=乡镇)
// - parent_code: 父级地区代码（获取下级地区时使用）

// 示例1: 获取省份列表
// GET /app/allUser/findDistricts?level=0
{
  "code": 0,
  "message": "获取地区列表成功",
  "data": [
    {
      "_id": "HSh3ajtqN",
      "id": "660000000",
      "parent_code": "0",
      "name": "建设兵团",
      "merger_name": "建设兵团",
      "area_code": "660000000",
      "city_code": "660000000",
      "lat": "43.793026",
      "lng": "87.627704",
      "level": "0",
      "short_name": "建设兵团",
      "pinyin": "JianSheBingTuan"
    }
  ]
}

// 示例2: 获取下级地区
// GET /app/allUser/findDistricts?level=1&parent_code=660000000
{
  "code": 0,
  "message": "获取地区列表成功",
  "data": [
    {
      "_id": "rvvQtQijZQ4B",
      "id": "661200000",
      "parent_code": "660000000",
      "name": "建设兵团第二师",
      "level": "1",
      // ... 其他字段
    }
  ]
}
```

### 数据字段说明
- `id`: 地区唯一标识
- `parent_code`: 父级地区代码
- `name`: 地区名称
- `level`: 地区级别 (0-3)
- `area_code`/`city_code`: 地区代码
- `short_name`: 简称
- `pinyin`: 拼音

## 注意事项

1. **真实API接口**：组件已适配真实的后端API接口 `/app/allUser/findDistricts`
2. **响应格式**：接口成功状态为 `status: 200`（不是 `code: 0`）
3. **任意级别支持**：支持1-4级任意选择，通过 `level` 属性控制
4. **数据格式**：返回地区名称数组格式，如 `['北京市', '北京市', '东城区', '东华门街道']`
5. **错误处理**：组件内置了错误处理和加载状态
6. **性能优化**：数据懒加载，只在需要时获取下级数据
7. **兼容性**：同时支持新的数组格式和旧的对象格式
8. **后端表结构**：后端已修改为数组格式存储（`nativePlace: [String]`）

## 集成到现有项目

在 `pages_user/pages/eHealthRecord/basicInfo.vue` 中已经集成了该组件：

1. 替换了户籍所在地和常住所在地的输入框
2. 添加了相应的数据处理方法
3. 保存时同时更新新旧两种数据格式

## 后续优化

1. **缓存机制**：添加地区数据缓存，减少重复请求
2. **搜索功能**：支持地区名称搜索
3. **自定义样式**：支持更多样式自定义选项
4. **国际化**：支持多语言
