// 地区API接口测试工具
import recordApi from '@/api/record.js'

/**
 * 测试地区API接口
 */
export class AreaApiTest {
  
  /**
   * 测试获取省份列表
   */
  static async testGetProvinces() {
    console.log('=== 测试获取省份列表 ===')
    try {
      const res = await recordApi.getProvinces()
      console.log('响应结果:', res)
      
      if (res.code === 0) {
        console.log('✅ 获取省份列表成功')
        console.log(`📊 共获取到 ${res.data.length} 个省份`)
        if (res.data.length > 0) {
          console.log('📋 第一个省份:', res.data[0])
        }
        return res.data
      } else {
        console.log('❌ 获取省份列表失败:', res.message)
        return []
      }
    } catch (error) {
      console.error('❌ 获取省份列表异常:', error)
      return []
    }
  }
  
  /**
   * 测试获取城市列表
   */
  static async testGetCities(provinceCode) {
    console.log(`=== 测试获取城市列表 (省份代码: ${provinceCode}) ===`)
    try {
      const res = await recordApi.getCitiesByProvince(provinceCode)
      console.log('响应结果:', res)
      
      if (res.code === 0) {
        console.log('✅ 获取城市列表成功')
        console.log(`📊 共获取到 ${res.data.length} 个城市`)
        if (res.data.length > 0) {
          console.log('📋 第一个城市:', res.data[0])
        }
        return res.data
      } else {
        console.log('❌ 获取城市列表失败:', res.message)
        return []
      }
    } catch (error) {
      console.error('❌ 获取城市列表异常:', error)
      return []
    }
  }
  
  /**
   * 测试获取区县列表
   */
  static async testGetDistricts(cityCode) {
    console.log(`=== 测试获取区县列表 (城市代码: ${cityCode}) ===`)
    try {
      const res = await recordApi.getDistrictsByCity(cityCode)
      console.log('响应结果:', res)
      
      if (res.code === 0) {
        console.log('✅ 获取区县列表成功')
        console.log(`📊 共获取到 ${res.data.length} 个区县`)
        if (res.data.length > 0) {
          console.log('📋 第一个区县:', res.data[0])
        }
        return res.data
      } else {
        console.log('❌ 获取区县列表失败:', res.message)
        return []
      }
    } catch (error) {
      console.error('❌ 获取区县列表异常:', error)
      return []
    }
  }
  
  /**
   * 测试获取乡镇列表
   */
  static async testGetTowns(districtCode) {
    console.log(`=== 测试获取乡镇列表 (区县代码: ${districtCode}) ===`)
    try {
      const res = await recordApi.getTownsByDistrict(districtCode)
      console.log('响应结果:', res)
      
      if (res.code === 0) {
        console.log('✅ 获取乡镇列表成功')
        console.log(`📊 共获取到 ${res.data.length} 个乡镇`)
        if (res.data.length > 0) {
          console.log('📋 第一个乡镇:', res.data[0])
        }
        return res.data
      } else {
        console.log('❌ 获取乡镇列表失败:', res.message)
        return []
      }
    } catch (error) {
      console.error('❌ 获取乡镇列表异常:', error)
      return []
    }
  }
  
  /**
   * 完整的四级联动测试
   */
  static async testFullChain() {
    console.log('🚀 开始完整的四级联动测试')
    
    // 1. 获取省份
    const provinces = await this.testGetProvinces()
    if (provinces.length === 0) {
      console.log('❌ 无法获取省份数据，测试终止')
      return
    }
    
    // 2. 获取第一个省份的城市
    const firstProvince = provinces[0]
    const cities = await this.testGetCities(firstProvince.id)
    if (cities.length === 0) {
      console.log('❌ 无法获取城市数据，测试终止')
      return
    }
    
    // 3. 获取第一个城市的区县
    const firstCity = cities[0]
    const districts = await this.testGetDistricts(firstCity.id)
    if (districts.length === 0) {
      console.log('❌ 无法获取区县数据，测试终止')
      return
    }
    
    // 4. 获取第一个区县的乡镇
    const firstDistrict = districts[0]
    const towns = await this.testGetTowns(firstDistrict.id)
    
    console.log('🎉 四级联动测试完成')
    console.log('📊 测试结果汇总:')
    console.log(`   省份: ${firstProvince.name} (${firstProvince.id})`)
    console.log(`   城市: ${firstCity.name} (${firstCity.id})`)
    console.log(`   区县: ${firstDistrict.name} (${firstDistrict.id})`)
    if (towns.length > 0) {
      console.log(`   乡镇: ${towns[0].name} (${towns[0].id})`)
    } else {
      console.log('   乡镇: 无数据')
    }
  }
  
  /**
   * 测试直接调用findDistricts接口
   */
  static async testFindDistricts(level, parentCode = null) {
    console.log(`=== 测试findDistricts接口 (level: ${level}, parent_code: ${parentCode}) ===`)
    try {
      const params = { level }
      if (parentCode) {
        params.parent_code = parentCode
      }
      
      const res = await recordApi.findDistricts(params)
      console.log('响应结果:', res)
      
      if (res.code === 0) {
        console.log('✅ 调用成功')
        console.log(`📊 共获取到 ${res.data.length} 条数据`)
        return res.data
      } else {
        console.log('❌ 调用失败:', res.message)
        return []
      }
    } catch (error) {
      console.error('❌ 调用异常:', error)
      return []
    }
  }
}

// 使用示例：
// 在页面中调用测试
// import { AreaApiTest } from '@/utils/areaApiTest.js'
// 
// // 测试获取省份
// AreaApiTest.testGetProvinces()
// 
// // 完整测试
// AreaApiTest.testFullChain()
// 
// // 直接测试接口
// AreaApiTest.testFindDistricts(0) // 获取省份
// AreaApiTest.testFindDistricts(1, '660000000') // 获取指定省份的城市
