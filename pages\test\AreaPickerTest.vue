<template>
  <view class="container">
    <view class="header">
      <text class="title">地区选择器测试页面</text>
    </view>
    
    <view class="section">
      <view class="section-title">四级选择器（省市区乡镇）</view>
      <view class="form-item">
        <text class="label">选择地区：</text>
        <AreaPicker 
          :value="area4Level" 
          :isEditing="true"
          :level="4"
          placeholder="请选择省市区乡镇"
          @change="handleArea4Change"
        />
      </view>
      <view class="result">
        <text class="result-title">选择结果：</text>
        <text class="result-text">{{ getDisplayText(area4Level) }}</text>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">一级选择器（省）</view>
      <view class="form-item">
        <text class="label">选择地区：</text>
        <AreaPicker
          :value="area1Level"
          :isEditing="true"
          :level="1"
          placeholder="请选择省份"
          @change="handleArea1Change"
        />
      </view>
      <view class="result">
        <text class="result-title">选择结果：</text>
        <text class="result-text">{{ getDisplayText(area1Level) }}</text>
      </view>
    </view>

    <view class="section">
      <view class="section-title">二级选择器（省市）</view>
      <view class="form-item">
        <text class="label">选择地区：</text>
        <AreaPicker
          :value="area2Level"
          :isEditing="true"
          :level="2"
          placeholder="请选择省市"
          @change="handleArea2Change"
        />
      </view>
      <view class="result">
        <text class="result-title">选择结果：</text>
        <text class="result-text">{{ getDisplayText(area2Level) }}</text>
      </view>
    </view>

    <view class="section">
      <view class="section-title">三级选择器（省市区）</view>
      <view class="form-item">
        <text class="label">选择地区：</text>
        <AreaPicker
          :value="area3Level"
          :isEditing="true"
          :level="3"
          placeholder="请选择省市区"
          @change="handleArea3Change"
        />
      </view>
      <view class="result">
        <text class="result-title">选择结果：</text>
        <text class="result-text">{{ getDisplayText(area3Level) }}</text>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">显示模式</view>
      <view class="form-item">
        <text class="label">户籍所在地：</text>
        <AreaPicker 
          :value="displayArea" 
          :isEditing="false"
          :level="4"
          placeholder="暂无数据"
        />
      </view>
    </view>
    
    <view class="actions">
      <button class="btn" @click="resetData">重置数据</button>
      <button class="btn" @click="setTestData">设置测试数据</button>
    </view>
  </view>
</template>

<script>
import AreaPicker from '@/components/AreaPicker.vue'

export default {
  components: {
    AreaPicker
  },
  
  data() {
    return {
      area4Level: [],
      area3Level: [],
      area1Level: [],
      area2Level: [],
      displayArea: ['北京市', '北京市', '东城区', '东华门街道']
    }
  },
  
  methods: {
    handleArea1Change(value) {
      this.area1Level = value
      console.log('一级选择器变化:', value)
    },

    handleArea2Change(value) {
      this.area2Level = value
      console.log('二级选择器变化:', value)
    },

    handleArea3Change(value) {
      this.area3Level = value
      console.log('三级选择器变化:', value)
    },

    handleArea4Change(value) {
      this.area4Level = value
      console.log('四级选择器变化:', value)
    },

    getDisplayText(area) {
      if (!area || !Array.isArray(area) || area.length === 0) {
        return '未选择'
      }
      return area.join(' ')
    },
    
    resetData() {
      this.area1Level = []
      this.area2Level = []
      this.area3Level = []
      this.area4Level = []
      uni.showToast({
        title: '数据已重置',
        icon: 'success'
      })
    },

    setTestData() {
      // 使用真实的地区名称进行测试
      this.area1Level = ['建设兵团']
      this.area2Level = ['建设兵团', '建设兵团第二师']
      this.area3Level = ['建设兵团', '建设兵团第二师', '铁门关市']
      this.area4Level = ['建设兵团', '建设兵团第二师', '铁门关市', '二十九团']
      uni.showToast({
        title: '测试数据已设置',
        icon: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
    border-left: 8rpx solid #007AFF;
    padding-left: 20rpx;
  }
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  
  .label {
    width: 200rpx;
    font-size: 28rpx;
    color: #666;
    flex-shrink: 0;
  }
}

.result {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  border-left: 6rpx solid #28a745;
  
  .result-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 15rpx;
  }
  
  .result-text {
    font-size: 28rpx;
    color: #007AFF;
    display: block;
    margin-bottom: 10rpx;
  }
  
  .result-codes {
    font-size: 24rpx;
    color: #666;
    display: block;
  }
}

.actions {
  display: flex;
  justify-content: space-around;
  margin-top: 60rpx;
  
  .btn {
    background: #007AFF;
    color: #fff;
    border: none;
    border-radius: 12rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
    
    &:active {
      background: #0056b3;
    }
  }
}
</style>
