<template>
  <view class="container">
    <view class="header">
      <text class="title">地区选择器测试页面</text>
    </view>
    
    <view class="section">
      <view class="section-title">四级选择器（省市区乡镇）</view>
      <view class="form-item">
        <text class="label">选择地区：</text>
        <AreaPicker 
          :value="area4Level" 
          :isEditing="true"
          :level="4"
          placeholder="请选择省市区乡镇"
          @change="handleArea4Change"
        />
      </view>
      <view class="result">
        <text class="result-title">选择结果：</text>
        <text class="result-text">{{ getDisplayText(area4Level) }}</text>
        <text class="result-codes">代码：{{ area4Level.codes.join(' > ') }}</text>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">三级选择器（省市区）</view>
      <view class="form-item">
        <text class="label">选择地区：</text>
        <AreaPicker 
          :value="area3Level" 
          :isEditing="true"
          :level="3"
          placeholder="请选择省市区"
          @change="handleArea3Change"
        />
      </view>
      <view class="result">
        <text class="result-title">选择结果：</text>
        <text class="result-text">{{ getDisplayText(area3Level) }}</text>
        <text class="result-codes">代码：{{ area3Level.codes.join(' > ') }}</text>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">显示模式</view>
      <view class="form-item">
        <text class="label">户籍所在地：</text>
        <AreaPicker 
          :value="displayArea" 
          :isEditing="false"
          :level="4"
          placeholder="暂无数据"
        />
      </view>
    </view>
    
    <view class="actions">
      <button class="btn" @click="resetData">重置数据</button>
      <button class="btn" @click="setTestData">设置测试数据</button>
    </view>
  </view>
</template>

<script>
import AreaPicker from '@/components/AreaPicker.vue'

export default {
  components: {
    AreaPicker
  },
  
  data() {
    return {
      area4Level: { codes: [], names: [] },
      area3Level: { codes: [], names: [] },
      displayArea: { 
        codes: ['110000', '110100', '110101', '110101001'], 
        names: ['北京市', '北京市', '东城区', '东华门街道'] 
      }
    }
  },
  
  methods: {
    handleArea4Change(value) {
      this.area4Level = value
      console.log('四级选择器变化:', value)
    },
    
    handleArea3Change(value) {
      this.area3Level = value
      console.log('三级选择器变化:', value)
    },
    
    getDisplayText(area) {
      if (!area || !area.names || area.names.length === 0) {
        return '未选择'
      }
      return area.names.join(' ')
    },
    
    resetData() {
      this.area4Level = { codes: [], names: [] }
      this.area3Level = { codes: [], names: [] }
      uni.showToast({
        title: '数据已重置',
        icon: 'success'
      })
    },
    
    setTestData() {
      // 使用真实的地区代码进行测试
      this.area4Level = {
        codes: ['660000000', '661200000', '661201000', '661201001'],
        names: ['建设兵团', '建设兵团第二师', '铁门关市', '二十九团']
      }
      this.area3Level = {
        codes: ['110000000', '110100000', '110101000'],
        names: ['北京市', '北京市', '东城区']
      }
      uni.showToast({
        title: '测试数据已设置',
        icon: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
    border-left: 8rpx solid #007AFF;
    padding-left: 20rpx;
  }
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  
  .label {
    width: 200rpx;
    font-size: 28rpx;
    color: #666;
    flex-shrink: 0;
  }
}

.result {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  border-left: 6rpx solid #28a745;
  
  .result-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 15rpx;
  }
  
  .result-text {
    font-size: 28rpx;
    color: #007AFF;
    display: block;
    margin-bottom: 10rpx;
  }
  
  .result-codes {
    font-size: 24rpx;
    color: #666;
    display: block;
  }
}

.actions {
  display: flex;
  justify-content: space-around;
  margin-top: 60rpx;
  
  .btn {
    background: #007AFF;
    color: #fff;
    border: none;
    border-radius: 12rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
    
    &:active {
      background: #0056b3;
    }
  }
}
</style>
