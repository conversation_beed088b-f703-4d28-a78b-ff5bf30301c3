// 模拟地区数据，用于四级地区选择器
// 实际项目中应该从后端API获取

// 省份数据
export const provinces = [
  { label: "北京市", value: "110000" },
  { label: "天津市", value: "120000" },
  { label: "河北省", value: "130000" },
  { label: "山西省", value: "140000" },
  { label: "内蒙古自治区", value: "150000" },
  { label: "辽宁省", value: "210000" },
  { label: "吉林省", value: "220000" },
  { label: "黑龙江省", value: "230000" },
  { label: "上海市", value: "310000" },
  { label: "江苏省", value: "320000" },
  { label: "浙江省", value: "330000" },
  { label: "安徽省", value: "340000" },
  { label: "福建省", value: "350000" },
  { label: "江西省", value: "360000" },
  { label: "山东省", value: "370000" },
  { label: "河南省", value: "410000" },
  { label: "湖北省", value: "420000" },
  { label: "湖南省", value: "430000" },
  { label: "广东省", value: "440000" },
  { label: "广西壮族自治区", value: "450000" },
  { label: "海南省", value: "460000" },
  { label: "重庆市", value: "500000" },
  { label: "四川省", value: "510000" },
  { label: "贵州省", value: "520000" },
  { label: "云南省", value: "530000" },
  { label: "西藏自治区", value: "540000" },
  { label: "陕西省", value: "610000" },
  { label: "甘肃省", value: "620000" },
  { label: "青海省", value: "630000" },
  { label: "宁夏回族自治区", value: "640000" },
  { label: "新疆维吾尔自治区", value: "650000" }
];

// 城市数据映射
export const citiesMap = {
  "110000": [
    { label: "北京市", value: "110100" }
  ],
  "120000": [
    { label: "天津市", value: "120100" }
  ],
  "130000": [
    { label: "石家庄市", value: "130100" },
    { label: "唐山市", value: "130200" },
    { label: "秦皇岛市", value: "130300" },
    { label: "邯郸市", value: "130400" },
    { label: "邢台市", value: "130500" },
    { label: "保定市", value: "130600" },
    { label: "张家口市", value: "130700" },
    { label: "承德市", value: "130800" },
    { label: "沧州市", value: "130900" },
    { label: "廊坊市", value: "131000" },
    { label: "衡水市", value: "131100" }
  ],
  "440000": [
    { label: "广州市", value: "440100" },
    { label: "韶关市", value: "440200" },
    { label: "深圳市", value: "440300" },
    { label: "珠海市", value: "440400" },
    { label: "汕头市", value: "440500" },
    { label: "佛山市", value: "440600" },
    { label: "江门市", value: "440700" },
    { label: "湛江市", value: "440800" },
    { label: "茂名市", value: "440900" },
    { label: "肇庆市", value: "441200" },
    { label: "惠州市", value: "441300" },
    { label: "梅州市", value: "441400" },
    { label: "汕尾市", value: "441500" },
    { label: "河源市", value: "441600" },
    { label: "阳江市", value: "441700" },
    { label: "清远市", value: "441800" },
    { label: "东莞市", value: "441900" },
    { label: "中山市", value: "442000" },
    { label: "潮州市", value: "445100" },
    { label: "揭阳市", value: "445200" },
    { label: "云浮市", value: "445300" }
  ]
};

// 区县数据映射
export const districtsMap = {
  "110100": [
    { label: "东城区", value: "110101" },
    { label: "西城区", value: "110102" },
    { label: "朝阳区", value: "110105" },
    { label: "丰台区", value: "110106" },
    { label: "石景山区", value: "110107" },
    { label: "海淀区", value: "110108" },
    { label: "门头沟区", value: "110109" },
    { label: "房山区", value: "110111" },
    { label: "通州区", value: "110112" },
    { label: "顺义区", value: "110113" },
    { label: "昌平区", value: "110114" },
    { label: "大兴区", value: "110115" },
    { label: "怀柔区", value: "110116" },
    { label: "平谷区", value: "110117" },
    { label: "密云区", value: "110118" },
    { label: "延庆区", value: "110119" }
  ],
  "440100": [
    { label: "荔湾区", value: "440103" },
    { label: "越秀区", value: "440104" },
    { label: "海珠区", value: "440105" },
    { label: "天河区", value: "440106" },
    { label: "白云区", value: "440111" },
    { label: "黄埔区", value: "440112" },
    { label: "番禺区", value: "440113" },
    { label: "花都区", value: "440114" },
    { label: "南沙区", value: "440115" },
    { label: "从化区", value: "440117" },
    { label: "增城区", value: "440118" }
  ],
  "440300": [
    { label: "罗湖区", value: "440303" },
    { label: "福田区", value: "440304" },
    { label: "南山区", value: "440305" },
    { label: "宝安区", value: "440306" },
    { label: "龙岗区", value: "440307" },
    { label: "盐田区", value: "440308" },
    { label: "龙华区", value: "440309" },
    { label: "坪山区", value: "440310" },
    { label: "光明区", value: "440311" }
  ]
};

// 乡镇数据映射
export const townsMap = {
  "110101": [
    { label: "东华门街道", value: "110101001" },
    { label: "景山街道", value: "110101002" },
    { label: "交道口街道", value: "110101003" },
    { label: "安定门街道", value: "110101004" },
    { label: "北新桥街道", value: "110101005" },
    { label: "东四街道", value: "110101006" },
    { label: "朝阳门街道", value: "110101007" },
    { label: "建国门街道", value: "110101008" },
    { label: "东直门街道", value: "110101009" },
    { label: "和平里街道", value: "110101010" }
  ],
  "110102": [
    { label: "西长安街街道", value: "110102001" },
    { label: "新街口街道", value: "110102002" },
    { label: "月坛街道", value: "110102003" },
    { label: "展览路街道", value: "110102004" },
    { label: "德胜街道", value: "110102005" },
    { label: "金融街街道", value: "110102006" },
    { label: "什刹海街道", value: "110102007" },
    { label: "大栅栏街道", value: "110102008" },
    { label: "天桥街道", value: "110102009" },
    { label: "椿树街道", value: "110102010" }
  ],
  "440103": [
    { label: "沙面街道", value: "440103001" },
    { label: "岭南街道", value: "440103002" },
    { label: "华林街道", value: "440103003" },
    { label: "多宝街道", value: "440103004" },
    { label: "昌华街道", value: "440103005" },
    { label: "逢源街道", value: "440103006" },
    { label: "龙津街道", value: "440103007" },
    { label: "金花街道", value: "440103008" },
    { label: "彩虹街道", value: "440103009" },
    { label: "南源街道", value: "440103010" }
  ],
  "440104": [
    { label: "洪桥街道", value: "440104001" },
    { label: "北京街道", value: "440104002" },
    { label: "六榕街道", value: "440104003" },
    { label: "流花街道", value: "440104004" },
    { label: "光塔街道", value: "440104005" },
    { label: "人民街道", value: "440104006" },
    { label: "东山街道", value: "440104007" },
    { label: "农林街道", value: "440104008" },
    { label: "梅花村街道", value: "440104009" },
    { label: "黄花岗街道", value: "440104010" }
  ]
};

// 模拟API函数
export function getProvinces() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 200,
        data: provinces
      });
    }, 100);
  });
}

export function getCitiesByProvince(provinceCode) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 200,
        data: citiesMap[provinceCode] || []
      });
    }, 100);
  });
}

export function getDistrictsByCity(cityCode) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 200,
        data: districtsMap[cityCode] || []
      });
    }, 100);
  });
}

export function getTownsByDistrict(districtCode) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: 200,
        data: townsMap[districtCode] || []
      });
    }, 100);
  });
}
