<template>
  <view class="area-picker">
    <!-- 显示选择结果 -->
    <view class="area-display" @click="showPicker" v-if="!isEditing">
      <text class="area-text">{{ displayText || placeholder }}</text>
      <uni-icons type="arrowdown" size="16" color="#999"></uni-icons>
    </view>
    
    <!-- 编辑模式下的输入框样式 -->
    <view class="area-input" @click="showPicker" v-else>
      <text class="area-text" :class="{ placeholder: !displayText }">{{ displayText || placeholder }}</text>
      <uni-icons type="arrowdown" size="16" color="#999"></uni-icons>
    </view>

    <!-- 弹窗选择器 -->
    <uni-popup ref="popup" type="bottom">
      <view class="picker-container">
        <!-- 头部 -->
        <view class="picker-header">
          <text class="cancel-btn" @click="cancel">取消</text>
          <text class="title">选择地区</text>
          <text class="confirm-btn" @click="confirm">确定</text>
        </view>
        
        <!-- 选择器内容 -->
        <view class="picker-content">
          <picker-view 
            :indicator-style="indicatorStyle" 
            :value="pickerValue" 
            @change="onPickerChange"
            class="picker-view"
          >
            <!-- 省份 -->
            <picker-view-column>
              <view class="picker-item" v-for="(item, index) in provinces" :key="item.value">
                <text>{{ item.label }}</text>
              </view>
            </picker-view-column>
            
            <!-- 城市 -->
            <picker-view-column>
              <view class="picker-item" v-for="(item, index) in cities" :key="item.value">
                <text>{{ item.label }}</text>
              </view>
            </picker-view-column>
            
            <!-- 区县 -->
            <picker-view-column>
              <view class="picker-item" v-for="(item, index) in districts" :key="item.value">
                <text>{{ item.label }}</text>
              </view>
            </picker-view-column>
            
            <!-- 乡镇 -->
            <picker-view-column v-if="level >= 4">
              <view class="picker-item" v-for="(item, index) in towns" :key="item.value">
                <text>{{ item.label }}</text>
              </view>
            </picker-view-column>
          </picker-view>
        </view>
        
        <!-- 加载状态 -->
        <view class="loading-mask" v-if="loading">
          <uni-icons type="spinner-cycle" size="20" color="#007AFF"></uni-icons>
          <text class="loading-text">加载中...</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import recordApi from '@/api/record.js'
import { getProvinces, getCitiesByProvince, getDistrictsByCity, getTownsByDistrict } from '@/utils/mockAreaData.js'

export default {
  name: 'AreaPicker',
  props: {
    // 当前值，格式：{ codes: ['110000', '110100', '110101', '110101001'], names: ['北京市', '北京市', '东城区', '东华门街道'] }
    value: {
      type: Object,
      default: () => ({ codes: [], names: [] })
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择地区'
    },
    // 是否为编辑模式
    isEditing: {
      type: Boolean,
      default: false
    },
    // 选择级别，3=省市区，4=省市区乡镇
    level: {
      type: Number,
      default: 4
    }
  },
  
  data() {
    return {
      // 选择器数据
      provinces: [],
      cities: [],
      districts: [],
      towns: [],
      
      // 选择器当前值
      pickerValue: [0, 0, 0, 0],
      
      // 临时选择的值
      tempValue: { codes: [], names: [] },
      
      // 加载状态
      loading: false,
      
      // 样式
      indicatorStyle: 'height: 50px;'
    }
  },
  
  computed: {
    // 显示文本
    displayText() {
      if (!this.value || !this.value.names || this.value.names.length === 0) {
        return ''
      }
      return this.value.names.join(' ')
    }
  },
  
  watch: {
    value: {
      handler(newVal) {
        if (newVal && newVal.codes && newVal.codes.length > 0) {
          this.initPickerValue()
        }
      },
      immediate: true,
      deep: true
    }
  },
  
  async created() {
    await this.loadProvinces()
  },
  
  methods: {
    // 显示选择器
    async showPicker() {
      this.tempValue = JSON.parse(JSON.stringify(this.value))
      await this.initPickerData()
      this.$refs.popup.open()
    },
    
    // 加载省份数据
    async loadProvinces() {
      try {
        this.loading = true
        // 使用模拟数据，实际项目中应该使用 recordApi.getProvinces()
        const res = await getProvinces()
        if (res.status === 200) {
          this.provinces = res.data || []
        }
      } catch (error) {
        console.error('加载省份数据失败:', error)
        uni.showToast({
          title: '加载省份数据失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 加载城市数据
    async loadCities(provinceCode) {
      if (!provinceCode) {
        this.cities = []
        return
      }

      try {
        this.loading = true
        // 使用模拟数据，实际项目中应该使用 recordApi.getCitiesByProvince({ provinceCode })
        const res = await getCitiesByProvince(provinceCode)
        if (res.status === 200) {
          this.cities = res.data || []
        }
      } catch (error) {
        console.error('加载城市数据失败:', error)
        this.cities = []
      } finally {
        this.loading = false
      }
    },
    
    // 加载区县数据
    async loadDistricts(cityCode) {
      if (!cityCode) {
        this.districts = []
        return
      }

      try {
        this.loading = true
        // 使用模拟数据，实际项目中应该使用 recordApi.getDistrictsByCity({ cityCode })
        const res = await getDistrictsByCity(cityCode)
        if (res.status === 200) {
          this.districts = res.data || []
        }
      } catch (error) {
        console.error('加载区县数据失败:', error)
        this.districts = []
      } finally {
        this.loading = false
      }
    },

    // 加载乡镇数据
    async loadTowns(districtCode) {
      if (!districtCode || this.level < 4) {
        this.towns = []
        return
      }

      try {
        this.loading = true
        // 使用模拟数据，实际项目中应该使用 recordApi.getTownsByDistrict({ districtCode })
        const res = await getTownsByDistrict(districtCode)
        if (res.status === 200) {
          this.towns = res.data || []
        }
      } catch (error) {
        console.error('加载乡镇数据失败:', error)
        this.towns = []
      } finally {
        this.loading = false
      }
    },
    
    // 初始化选择器数据
    async initPickerData() {
      if (this.tempValue.codes && this.tempValue.codes.length > 0) {
        // 根据已选择的值加载对应的数据
        if (this.tempValue.codes[0]) {
          await this.loadCities(this.tempValue.codes[0])
        }
        if (this.tempValue.codes[1]) {
          await this.loadDistricts(this.tempValue.codes[1])
        }
        if (this.tempValue.codes[2] && this.level >= 4) {
          await this.loadTowns(this.tempValue.codes[2])
        }
        this.updatePickerValue()
      } else {
        // 默认加载第一个省份的城市
        if (this.provinces.length > 0) {
          await this.loadCities(this.provinces[0].value)
          if (this.cities.length > 0) {
            await this.loadDistricts(this.cities[0].value)
            if (this.districts.length > 0 && this.level >= 4) {
              await this.loadTowns(this.districts[0].value)
            }
          }
        }
      }
    },
    
    // 初始化选择器位置
    initPickerValue() {
      if (!this.value.codes || this.value.codes.length === 0) return
      
      const newPickerValue = [0, 0, 0, 0]
      
      // 查找省份索引
      const provinceIndex = this.provinces.findIndex(item => item.value === this.value.codes[0])
      if (provinceIndex >= 0) newPickerValue[0] = provinceIndex
      
      // 查找城市索引
      const cityIndex = this.cities.findIndex(item => item.value === this.value.codes[1])
      if (cityIndex >= 0) newPickerValue[1] = cityIndex
      
      // 查找区县索引
      const districtIndex = this.districts.findIndex(item => item.value === this.value.codes[2])
      if (districtIndex >= 0) newPickerValue[2] = districtIndex
      
      // 查找乡镇索引
      if (this.level >= 4 && this.value.codes[3]) {
        const townIndex = this.towns.findIndex(item => item.value === this.value.codes[3])
        if (townIndex >= 0) newPickerValue[3] = townIndex
      }
      
      this.pickerValue = newPickerValue
    },
    
    // 更新选择器位置
    updatePickerValue() {
      this.initPickerValue()
    },
    
    // 选择器变化事件
    async onPickerChange(e) {
      const value = e.detail.value
      const oldValue = [...this.pickerValue]
      this.pickerValue = value
      
      // 省份变化
      if (value[0] !== oldValue[0]) {
        this.pickerValue[1] = 0
        this.pickerValue[2] = 0
        this.pickerValue[3] = 0
        await this.loadCities(this.provinces[value[0]]?.value)
        if (this.cities.length > 0) {
          await this.loadDistricts(this.cities[0].value)
          if (this.districts.length > 0 && this.level >= 4) {
            await this.loadTowns(this.districts[0].value)
          }
        }
      }
      // 城市变化
      else if (value[1] !== oldValue[1]) {
        this.pickerValue[2] = 0
        this.pickerValue[3] = 0
        await this.loadDistricts(this.cities[value[1]]?.value)
        if (this.districts.length > 0 && this.level >= 4) {
          await this.loadTowns(this.districts[0].value)
        }
      }
      // 区县变化
      else if (value[2] !== oldValue[2] && this.level >= 4) {
        this.pickerValue[3] = 0
        await this.loadTowns(this.districts[value[2]]?.value)
      }
      
      this.updateTempValue()
    },
    
    // 更新临时值
    updateTempValue() {
      const codes = []
      const names = []
      
      if (this.provinces[this.pickerValue[0]]) {
        codes.push(this.provinces[this.pickerValue[0]].value)
        names.push(this.provinces[this.pickerValue[0]].label)
      }
      
      if (this.cities[this.pickerValue[1]]) {
        codes.push(this.cities[this.pickerValue[1]].value)
        names.push(this.cities[this.pickerValue[1]].label)
      }
      
      if (this.districts[this.pickerValue[2]]) {
        codes.push(this.districts[this.pickerValue[2]].value)
        names.push(this.districts[this.pickerValue[2]].label)
      }
      
      if (this.level >= 4 && this.towns[this.pickerValue[3]]) {
        codes.push(this.towns[this.pickerValue[3]].value)
        names.push(this.towns[this.pickerValue[3]].label)
      }
      
      this.tempValue = { codes, names }
    },
    
    // 确定选择
    confirm() {
      this.updateTempValue()
      this.$emit('change', this.tempValue)
      this.$refs.popup.close()
    },
    
    // 取消选择
    cancel() {
      this.$refs.popup.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.area-picker {
  width: 100%;
}

.area-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  background: transparent;

  .area-text {
    flex: 1;
    font-size: 28rpx;
    color: #333;
  }
}

.area-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 2rpx solid #e9ecef;

  .area-text {
    flex: 1;
    font-size: 28rpx;
    color: #333;

    &.placeholder {
      color: #999;
    }
  }
}

.picker-container {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 2rpx solid #f0f0f0;

  .cancel-btn {
    font-size: 32rpx;
    color: #666;
  }

  .title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }

  .confirm-btn {
    font-size: 32rpx;
    color: #007AFF;
  }
}

.picker-content {
  height: 500rpx;
  position: relative;
}

.picker-view {
  width: 100%;
  height: 100%;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;

  text {
    font-size: 28rpx;
    color: #333;
  }
}

.loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;

  .loading-text {
    margin-top: 20rpx;
    font-size: 24rpx;
    color: #666;
  }
}
</style>
